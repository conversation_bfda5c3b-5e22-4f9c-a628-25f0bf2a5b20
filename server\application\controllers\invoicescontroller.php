<?php

header('Access-Control-Allow-Origin: *');

class InvoicesController extends VanillaController {
	
	function beforeAction () {

    }
    
    function admin_getPaymentReport() {
        // init response
        $response = array('status' => 'OK', 'message' => 'N/A');

        ob_start();
        require_once(APP_ROOT . '/editor/app/payment_report.php');
        $data = ob_get_contents();
        ob_end_clean();
        $adata = json_decode($data,true);

        $event_id = $_POST['event_id'];

        // get bus invoices and merge to data
        $bus_invoices = $this->Invoice->queryWithResultSet("SELECT
                `registrations`.`id` AS 'registrations.id',
                `registrations`.`approved_date` AS 'registrations.approved_date',
                `registrations`.`approval_status` AS 'registrations.approval_status',
                (CASE 
                    WHEN invoices.meta_data IS NOT NULL THEN JSON_EXTRACT(meta_data, '$.amount')
                    ELSE `registrations`.`amount`
                END) AS 'registrations.amount',
                `players`.`first_name` AS 'players.first_name',
                `players`.`last_name` AS 'players.last_name',
                '' AS 'pgroups.name',
                `parens`.`name1` AS 'parens.name1',
                `parens`.`email1` AS 'parens.email1',
                `parens`.`hkfc_account` AS 'parens.hkfc_account',
                `invoices`.`id` AS 'invoices.id',
                `invoices`.`invoice_date` AS 'invoices.invoice_date',
                `invoices`.`invoice_number` AS 'invoices.invoice_number',
                `invoices`.`status` AS 'invoices.status' 
            FROM
                invoices
                LEFT JOIN registrations ON registrations.id = JSON_EXTRACT( meta_data, '$.registration_id' ) 
                JOIN `players` ON `registrations`.`player_id` = `players`.`id`
                JOIN `parens` ON `players`.`parent_id` = `parens`.`id`
            WHERE
                invoices.meta_data IS NOT NULL
                AND registrations.event_id = $event_id
        ");

        if ($bus_invoices['status'] == "OK") {
            $bus_invoices = $bus_invoices['info']['rows'];

            // reformate bus invoices to {"DT_RowId":"row_26581","registrations":{"approved_date":"14-Jul-2023 22:38:10","approval_status":"App\/Inv\/Mail","amount":"5160.00"},"players":{"first_name":"Julian","last_name":"Lee"},"pgroups":{"name":"U5"},"parens":{"name1":"Maggie","email1":"<EMAIL>","hkfc_account":""},"invoices":{"id":"11992","invoice_number":"JS2324-26581","status":"PAID"}}
            foreach ($bus_invoices as $key => $bus_invoice) {
                $bus_invoices[$key]['DT_RowId'] = 'row_' . $bus_invoice['registrations.id'];
                $bus_invoices[$key]['registrations'] = array(
                    'approved_date' => $bus_invoice['registrations.approved_date'],
                    'approval_status' => $bus_invoice['registrations.approval_status'],
                    'amount' => $bus_invoice['registrations.amount']
                );
                $bus_invoices[$key]['players'] = array(
                    'first_name' => $bus_invoice['players.first_name'],
                    'last_name' => $bus_invoice['players.last_name']
                );
                $bus_invoices[$key]['pgroups'] = array(
                    'name' => $bus_invoice['pgroups.name']
                );
                $bus_invoices[$key]['parens'] = array(
                    'name1' => $bus_invoice['parens.name1'],
                    'email1' => $bus_invoice['parens.email1'],
                    'hkfc_account' => $bus_invoice['parens.hkfc_account']
                );
                $bus_invoices[$key]['invoices'] = array(
                    'id' => $bus_invoice['invoices.id'],
                    'invoice_number' => $bus_invoice['invoices.invoice_number'],
                    'invoice_date' => $bus_invoice['invoices.invoice_date'],
                    'status' => $bus_invoice['invoices.status']
                );
            }

            $adata['data'] = array_merge($adata['data'], $bus_invoices);
        }

        $response['data'] = $adata['data'];
        $response['options'] = isset($adata['options'])? $adata['options'] : array();
        $response['files'] = isset($adata['files']) ? $adata['files'] : array();

        _json_echo('admin_getPaymentReport', $response);
    }

	function admin_reminderPayment() {
        // set basic data
        $response = array('status' => 'ERROR', 'message' => 'none');
        
        // get values
        $user_id = $_POST['user_id'];
        $event_id = $_POST['event_id'];
        $invoice_ids = $_POST['invoice_ids'];
        
        // encode POST data to JSON string, and decode to array object
        $post = json_encode($_POST);
        $apost = json_decode($post,true);
        $action = $apost['action'];
        $data = $apost['data'];

        // get subject, message and marchant
        $subject = $data['keyless']['subject'];
        // check subject
        if ($subject == '') {
            $response = array('status' => 'DATA_ERROR', 'message' => "error on data input");
            $data = array();
            $response['data'] = $data;
            $response['fieldErrors'] = array(array('name' => 'subject', 'status' => 'This field is required'));
            _json_echo('admin_reminderPayment', $response);
            return;
        }
        $merchant = false;
        if (isset($data['keyless']['merchant'])) {
            $merchant = true;
        }
        $note = $data['keyless']['note'];

        logError($event_id . " - " . $subject . " - " . $note . " - " . ($merchant ? "true" : "false"));

        // get all invoices
        $invoice_ids_str = implode(", ", $invoice_ids);
        $sql = "SELECT * FROM invoices WHERE id IN ($invoice_ids_str)";
        logError($sql);
        $invoices = $this->Invoice->queryWithResultSet($sql);

        if ($invoices['status'] == "OK") {
            $invoices = $invoices['info']['rows'];
            // set up paypal and mail
            require_once(FRAMEWORK_ROOT . '/mailer/Mailer.php');
            require_once(APP_ROOT . '/paypal/app/Paypal.php');
            $paypal = new PayPal();

            $total_invoice = count($invoices);
            $success_reminder = 0;
            $skip_reminder = 0;
            for ($i=0; $i < $total_invoice; $i++) { 
                // get invoice payment
                $inv = $invoices[$i];
                $status = $inv['status'];
                $result = $paypal->getInvoice($inv['invoice_identification']);
                error_log("admin_reminderPayment - getInvoice: " . print_r($result, true));
                if ($result['status'] == 'ERROR') {
                    // some time invoice is no longer valid, display error and just ignore
                    logError('admin_reconcilePayment() - getInvoice: ' . $result['value']);
                    continue;
                }

                // get payment status
                $invoice = $result['value'];

                // $new_status = $paypal->getInvoiceStatus($invoice);
                
                $new_status = $invoice->getStatus();
                if ($new_status != $status) {
                    // update status
                    $result = $this->Invoice->queryWithStatus("UPDATE invoices SET status = '" . $new_status . "' WHERE id=" . $inv['id']);
                    $status = $new_status;
                }

                // send reminder
                if ($status == "SENT") {
                    $reminder = $paypal->remindInvoice($invoice, $subject, $note, $merchant);
                    $success_reminder++;
                } else {
                    $skip_reminder++;
                    continue;
                }
            }
            // return result
            $response['status'] = "OK";
            $response['message'] = "There are ". $success_reminder . " reminder success, " . $skip_reminder . " reminder skip and " . ($total_invoice-$success_reminder-$skip_reminder) . " reminder fail.";
        } else {
            $response['message'] = 'System error. Please try again!';
        }

        $response['data'] = array();
        _json_echo('admin_reminderPayment', $response);
    }

    function admin_recordPayment() {
        // set basic data
        $response = array("status" => "ERROR", "message" => "none", "data" => array(), "fieldErrors" => array());

        // get values
        $user_id = $_POST['user_id'];
        $event_id = $_POST['event_id'];

        $invoice_number = $_POST['data']['keyless']['invoice_number'];
        $amount_due = $_POST['data']['keyless']['amount_due'];
        $payment_amount = $_POST['data']['keyless']['payment_amount'];
        $payment_date = $_POST['data']['keyless']['payment_date'];
        $payment_method = $_POST['data']['keyless']['payment_method'];
        $note = $_POST['data']['keyless']['note'];

        $payment_date = date('Y-m-d H:i:s', strtotime($payment_date));
        $payment_date = $payment_date . ' PST';
        $currency = "HKD";

        // check data
        if ($payment_amount > $amount_due) {
            $response['fieldErrors'][] = array("name" => "payment_amount", "status" => "Payment cannot be greater than the amount due");
            $response['status'] = "DATA_ERROR";
            _json_echo('admin_recordPayment', $response);
            return;
        }
        
        // get invoice details
        $invoice = $this->Invoice->queryWithOneResultSet("SELECT * FROM invoices WHERE invoice_number = '$invoice_number'");
        if ($invoice) {
            require_once(APP_ROOT . '/paypal/app/Paypal.php');
            $paypal = new PayPal();
            // get invoice
            $invoice_info = $paypal->getInvoice($invoice['invoice_identification']);

            if ($invoice_info['status'] == 'ERROR') {
                // some time invoice is no longer valid, display error and just ignore
                // error_log('admin_recordPayment() - getInvoice ERROR: ' . $invoice_info['value']);
                $response['message'] = "ERROR - getInvoice";
                _json_echo('admin_recordPayment', $response);
                return;
            } else {
                // error_log('admin_recordPayment() - getInvoice OK: ' . $invoice_info['value']);
                // get payment status
                $invoice = $invoice_info['value'];
                $new_status = $invoice->getStatus();
                $due_amount = $invoice->total_amount->value;
                // logError('admin_reconcilePayment() - getInvoiceStatus OK: ' . print_r($invoice, true));
                // $new_status = $result['value'];
                // record payment
                if ($new_status != "PAID") {

                    $paid_amount = $invoice->getPaidAmount();
                    if ($paid_amount != '') {
                        $paid_other = 0;
                        $paid_paypal = 0;
                        if (isset($paid_amount->other))
                            $paid_other = $paid_amount->other->value;
                        if (isset($paid_amount->paypal))
                            $paid_paypal = $paid_amount->paypal->value;
        
                        $paid_amount = $paid_other + $paid_paypal;
                    } else {
                        $paid_amount = 0;
                    }

                    if ($paid_amount < $due_amount) {
                        if (($payment_amount+$paid_amount) > $due_amount) {
                            $max_payment_amount = $due_amount - $paid_amount;
                            $response['message'] = 'Payment cannot be greater than $'. $max_payment_amount  . ' HKD. Please try again!';
                            _json_echo('admin_recordPayment', $response);
                            return;
                        }
                        $payment_record = $paypal->recordPayment($invoice, $payment_method, $payment_date, $note, $payment_amount, $currency);
                        if ($payment_record['status'] == "OK") {
                            $invoice_after_record = $paypal->getInvoice($invoice->id);
                            if ($invoice_info['status'] == 'ERROR') {
                                $response['message'] = "Something went wrong. Please try later";
                                _json_echo('admin_recordPayment', $response);
                                return;
                            } else {
                                $response['status'] = "OK";
                                $response['message'] = "Successful";
                                $invoice_after_record = $invoice_after_record['value'];
                                $status_after_record = $invoice_after_record->getStatus();
                                if ($new_status != $status_after_record) {
                                    $new_status = $status_after_record;
                                }
                                $result = $this->Invoice->queryWithStatus("UPDATE invoices SET status = '" . $new_status . "' WHERE invoice_number= '" . $invoice_number."'");
    
                            }
                        } else {
                            $response['message'] = $payment_record['value'];
                        }
                    } else {
                        $response['message'] = 'This invoice has paid!';
                    }
                } else {
                    $response['message'] = "This invoice has paid!";
                    $result = $this->Invoice->queryWithStatus("UPDATE invoices SET status = '" . $new_status . "' WHERE invoice_number= '" . $invoice_number."'");
                    _json_echo('admin_recordPayment', $response);
                    return;
                }
            }
                
        } else {
            $response['message'] = "Invalid data";
        }

        _json_echo('admin_recordPayment', $response);
    }

    function getInvoiceNumberAndAmount() {

        require_once(APP_ROOT . '/paypal/app/Paypal.php');
        $paypal = new PayPal();

        $response = array("status" => "ERROR", "message" => "none");

        // get all invoices
        $invoices = $this->Invoice->queryWithResultSet("SELECT invoices.*, registrations.id AS reg_id, registrations.amount AS reg_amount FROM invoices LEFT JOIN registrations ON invoices.registration_id = registrations.id WHERE invoice_number ='' OR invoice_number ='Array'");
        
        if ($invoices['status'] == "OK") {
            echo 'Get invoices -> OK<br/>';
            $invoices = $invoices['info']['rows'];
            // get info for each invoice
            foreach ($invoices as $key => $invoice) {

                // get invoice
                $invoice_identification = $invoice['invoice_identification'];
                echo '<br/>' . $key .' - ' . $invoice['id'] . ' - ' . $invoice_identification;
                $invoice_info = $paypal->getInvoice($invoice_identification);

                if ($invoice_info['status'] == 'ERROR') {
                    // some time invoice is no longer valid, display error and just ignore
                    // error_log('getInvoiceNumberAndAmount() - getInvoice: ' . $invoice_info['value']);
                    continue;
                } else {
                    $invoice_info = $invoice_info['value'];
                    // get invoice_number and amount
                    $invoice_number = $invoice_info->getNumber();
                    $amount = $paypal->getAmountAndCurrency($invoice_info);
                    
                    if ($amount['status'] == 'ERROR') {
                        // some error, can not continue, return
                        // error_log('getInvoiceNumberAndAmount() - getAmountAndCurrency: ' . $amount['value']);
                        continue;
                    } else {
                        $amount = $amount['value'];
                        $invoice_number = $invoice_number['value'];
                        
                        // update amount in registrations table
                        $sql1 = 'UPDATE registrations SET amount = "' . $amount['value'] . '" WHERE id = ' . $invoice['reg_id'];
                        $update_amount1 = $this->Invoice->queryWithStatus($sql1);
                        
                        // update invoice_number in invoices table
                        $sql2 = 'UPDATE invoices SET invoice_number = "' . $invoice_number . '", amount = "' . $amount['value'] . '", currency = "' . $amount['currency'] . '" WHERE id = ' . $invoice['id'];
                        $update_amount2 = $this->Invoice->queryWithStatus($sql2);
                        
                        if ($update_amount1['status'] == "OK" && $update_amount2['status'] == "OK") {
                            echo ' - update_amount -> OK';
                        } else {
                            echo ' - update_amount -> ERROR';
                        }
                        
                    }
                }
            }
        }

        _json_echo('getInvoiceNumberAndAmount', $response);
     
    }

    function checkInvoices() {

        require_once(APP_ROOT . '/paypal/app/Paypal.php');
        $paypal = new PayPal();

        $invoices = $this->Invoice->queryWithResultSet("SELECT * FROM invoices WHERE status = 'MARKED_AS_PAID' LIMIT 5");

        echo '<pre>';
        echo print_r($invoices);
        echo '</pre>';
        echo ' - getInvoice -> OK';

        if ($invoices['status'] == "OK") {
            $invoices = $invoices['info']['rows'];
            foreach ($invoices as $key => $invoice) {

                // if ($invoice['currency'] != "") {
                //     continue;
                // }

                $invoice_identification = $invoice['invoice_identification'];
                echo '<br/>' . $key .' - ' . $invoice['id'] . ' - ' . $invoice_identification;
                $invoice_info = $paypal->getInvoice($invoice_identification);
                if ($invoice_info['status'] == 'ERROR') {
                    // some time invoice is no longer valid, display error and just ignore
                    // error_log('admin_getPaymentInfo() - getInvoice: ' . $invoice_info['value']);
                    continue;
                } else {
                    echo '<pre>';
                    echo print_r($invoice_info);
                    echo '</pre>';
                    echo ' - getInvoice -> OK';

                    $result = $paypal->getAmountAndCurrency($invoice_info['value']);
                    
                    echo "<br/><br/>" . $result['value']['value'] . $result['value']['currency'] . "<br/><br/>";

                }
            }
        }

    }

    function checkTLS() {
        $ch = curl_init(); 
        curl_setopt($ch, CURLOPT_URL, "https://tlstest.paypal.com/"); 
        curl_setopt($ch, CURLOPT_CAINFO, dirname(__FILE__) . '/cacert.pem');
        // Some environments may be capable of TLS 1.2 but it is not in their list of defaults so need the SSL version option to be set.
        curl_setopt($ch, CURLOPT_SSLVERSION, 6);
        curl_exec($ch);
        echo "\n";
        if ($err = curl_error($ch)) {
            var_dump($err);
            echo "DEBUG INFORMATION:\n###########\n";
            echo "CURL VERSION:\n";
            echo json_encode(curl_version(), JSON_PRETTY_PRINT);
        }
    }

    function admin_checkRefundInvoice() {
        $response = array("status" => "ERROR", "data" => array(), 'message' => 'none');

        $invoice_id = $_POST['invoice_id'];
        require_once(APP_ROOT . '/paypal/app/Paypal.php');
        $paypal = new PayPal();

        $invoice_info = $this->Invoice->queryWithOneResultSet("SELECT invoice_identification, status FROM invoices WHERE id = $invoice_id");
        
        if ($invoice_info) {
            $invoice_identification = $invoice_info['invoice_identification'];
            $old_status = $invoice_info['status'];
            $invoice = $paypal->getInvoice($invoice_identification);
            
            if ($invoice['status'] == 'OK') {
                $invoice = $invoice['value'];
                $new_status = $invoice->getStatus();
                if ($new_status !== 'REFUNDED') {
                    $infos['invoice_identification'] = $invoice_identification;
                    // logError('admin_checkRefundInvoice - invoice: '. print_r($invoice, true));
            
                    if (isset($invoice->paid_amount->paypal)) {
                        $infos['original_payment'] = $invoice->paid_amount->paypal->value;
                        $payments =  $invoice->getPayments();
                        logError('admin_checkRefundInvoice - payments: '. print_r($payments, true));
                        $check_paypal_payment = false;
                        foreach($payments as $payment) {
                            if ($payment->type !== 'PAYPAL') {
                                continue;
                            } else {
                                $check_paypal_payment = true;
                                $transaction_id = $payment->transaction_id;
                                $infos['transaction_id'] = $transaction_id;
                                // Check refunded amount:
                                if (isset($invoice->refunded_amount->paypal->value)) {
                                    $infos['amount_remaining'] = $infos['original_payment'] - $invoice->refunded_amount->paypal->value;
                                } else {
                                    $infos['amount_remaining'] = $infos['original_payment'];
                                }
                                break;
                            }
                        }
                        if ($check_paypal_payment) {
                            $response['status'] = 'OK';
                            $response['data'] = $infos;
                        } else {
                            $response['message'] = "This invoice can't be refunded because it don't have any payments via paypal";
                        }
                    } else {
                        $response['message'] = "This invoice can't be refunded because it don't have any payments via paypal";
                    }
                } else {
                    $response['message'] = "This invoice has been refunded!";
                    $result = $this->Invoice->queryWithStatus("UPDATE invoices SET status = '" . $new_status . "' WHERE invoice_identification= '" . $invoice_identification."'");
                    _json_echo('admin_recordPayment', $response);
                    return;
    
                }
                
            } else {
                $response['message'] = "Can't get this invoice";
            }
        } else {
            $response['message'] = "Invalid data";
        }

        _json_echo('admin_checkRefundInvoice', $response);
    }

    function admin_refundPayment() {
        $response = array("status" => "ERROR", "message" => "none", "data" => array(), "fieldErrors" => array());
        
        //get informations
        $invoice_identification = $_POST['invoice_identification'];
        
        $amount = $_POST['data']['keyless']['refund_amount'];
        $transaction_id = $_POST['data']['keyless']['transaction_id'];
        $invoice_number = $_POST['data']['keyless']['invoice_number'];
        $reason = $_POST['data']['keyless']['reason'];
        $currency = "HKD";

        // get invoice
        require_once(APP_ROOT . '/paypal/app/Paypal.php');
        $paypal = new PayPal();
        $invoice_info = $paypal->getInvoice($invoice_identification);

        if ($invoice_info['status'] == 'ERROR') {
            // some time invoice is no longer valid, display error and just ignore
            $response['message'] = "ERROR - getInvoice";
            _json_echo('admin_refundPayment', $response);
            return;
        } else {
            // get refund status
            $invoice = $invoice_info['value'];
            $new_status = $invoice->getStatus();
            if ($new_status == '') {
                // error_log('admin_reconcilePayment() - getInvoiceStatus ERROR: ' . $result['value']);
                $response['message'] = "ERROR - getInvoice";
                _json_echo('admin_refundPayment', $response);
                return;
            } else {
                // record refund
                if ($new_status != "REFUNDED") {
                    $refund = $paypal->refund($transaction_id, $reason, $amount, $currency);
                    if ($refund['status'] == "OK") {
                            $invoice_after_refund = $paypal->getInvoice($invoice_identification);
                            if ($invoice_after_refund['status'] == 'OK') {
                                $invoice_after_refund = $invoice_after_refund['value'];
                                $status_after_refund = $invoice_after_refund->getStatus();
                            if ($new_status != $status_after_refund) {
                                $new_status = $status_after_refund;
                            }
                            $result = $this->Invoice->queryWithStatus("UPDATE invoices SET status = '" . $new_status . "' WHERE invoice_identification= '" . $invoice_identification."'");
                        $response['status'] = "OK";
                        $response['message'] = "Successful";
                        } else {
                            $response['message'] = 'Something went wrong. Please try later';
                        }
                    } else {
                        $response['message'] = $refund['value'];
                    }
                } else {
                    $response['message'] = "This invoice has refunded!";
                    $result = $this->Invoice->queryWithStatus("UPDATE invoices SET status = '" . $new_status . "' WHERE invoice_identification= '" . $invoice_identification."'");
                    _json_echo('admin_refundPayment', $response);
                    return;
                }
            }
        }
            _json_echo('admin_refundPayment', $response);
    }

    function admin_updatePaymentStatus() {
        $response = array('status' => 'ERROR', 'message' => 'none', 'changed_invoices' => array());
        
        // get values
        $user_id = $_POST['user_id'];
        $event_id = $_POST['event_id'];
        $invoice_ids = $_POST['invoice_ids'];

        // get all invoices
        $invoice_ids_str = implode(", ", $invoice_ids);
        $sql = "SELECT invoices.*, registrations.amount FROM invoices LEFT JOIN registrations ON invoices.registration_id = registrations.id WHERE invoices.id IN($invoice_ids_str)";
        error_log("admin_updatePaymentStatus - sql: " . $sql);
        $invoices = $this->Invoice->queryWithResultSet($sql);
        $changed_invoices = array();
        if ($invoices['status'] == "OK") {
            $invoices = $invoices['info']['rows'];
            // set up paypal and mail
        //     require_once(FRAMEWORK_ROOT . '/mailer/Mailer.php');
            require_once(APP_ROOT . '/paypal/app/Paypal.php');
            $paypal = new PayPal();

            $total_invoice = count($invoices);
            $count_update = 0;
            for ($i=0; $i < $total_invoice; $i++) { 
                // get invoice payment
                $inv = $invoices[$i];
                $status = $inv['status'];
                $amount = $inv['amount'];
                $result = $paypal->getInvoice($inv['invoice_identification']);
                if ($result['status'] == 'ERROR') {
                    // some time invoice is no longer valid, display error and just ignore
                    logError('admin_reconcilePayment() - getInvoice: ' . $result['value']);
                    continue;
                }

                // get payment status
                $invoice = $result['value'];
                $new_status = $invoice->getStatus();

                $new_amount = $invoice->getTotalAmount();
                $new_amount = $new_amount->value;
                if ($new_amount != $amount) {
                    $result = $this->Invoice->queryWithStatus("UPDATE registrations SET amount = " . $new_amount . " WHERE id=" . $inv['registration_id']);
                }
                if ($new_status == '') {
                    continue;
                }

                if ($new_status != $status) {
                    // update status
                    $result = $this->Invoice->queryWithStatus("UPDATE invoices SET status = '" . $new_status . "' WHERE id=" . $inv['id']);
                    $count_update++;
                    array_push($changed_invoices, array('invoice_number' => $inv['invoice_number'], 'business_name' => $invoice->billing_info[0]->business_name,'old_status' => $status, 'new_status' => $new_status));

                    // if invoice = PAID, then update 'team bus' flag to 'yes'
                    if ($new_status == 'PAID' || $new_status == 'MARKED_AS_PAID') {
                        $this->updateTeamBus($inv['registration_id'], $inv['meta_data']);
                    }
                }
            }
            // return result
            $response['status'] = "OK";
            if (count($changed_invoices) > 0) {
                $response['message'] = "There are $count_update invoices have updated in total $total_invoice invoices: ";
                for ($i = 0; $i < count($changed_invoices); $i++) {
                    $response['message'] .= '<br>'.$changed_invoices[$i]['business_name'] . ' (Invoice number: '. $changed_invoices[$i]['invoice_number'] .'): '. $changed_invoices[$i]['old_status']. ' => '. $changed_invoices[$i]['new_status'];
                }
                logError('admin_updatePaymentStatus - message: ' . $response['message']);
            } else
                 $response['message'] = "There are no invoices have updated";
        } else {
            $response['message'] = 'System error. Please try again!';
        }

        $response['data'] = array();
        _json_echo('admin_updatePaymentStatus', $response);
    }

    function admin_editInvoice() {
        $response = array('status' => 'ERROR', 'message' => 'none', 'data' => array());
        $reg_id = isset($_POST['reg_id']) ? $_POST['reg_id'] : 0;
        $old_amount = isset($_POST['old_amount']) ? $_POST['old_amount'] : 0;
        $data = isset($_POST['data']['keyless']) ? $_POST['data']['keyless'] : array();
        $invoice_number = isset($data['invoice_number']) ? $data['invoice_number'] : '';
        $edit_amount = isset($data['amount']) ? $data['amount'] : 0;

        // get registration by reg_id
        $registration = $this->Invoice->queryWithOneResultSet("SELECT * FROM registrations WHERE id = $reg_id");
        if (!$registration) {
            $response['message'] = 'No registration found for this invoice';
            _json_echo('admin_editInvoice', $response);
            return;
        }

        //get invoice_identification
        $invoice_info = $this->Invoice->queryWithOneResultSet("SELECT invoice_identification, status FROM invoices WHERE invoice_number = '$invoice_number'");
        // logError('admin_editInvoice - invoice_info: ' . print_r($invoice_info, true));
        if ($invoice_info) {
            $invoice_identification = $invoice_info['invoice_identification'];
            $status = $invoice_info['status'];
            require_once(APP_ROOT . '/paypal/app/Paypal.php');
            $paypal = new PayPal();

            //get Invoice by invoice_identification
            $invoice = $paypal->getInvoice($invoice_identification);
            
            if ($invoice['status'] == 'OK') {
                $invoice = $invoice['value'];
                // get new status and update if it's changed
                $new_status = $invoice->getStatus();
                if ($new_status != $status) {
                    // update status
                    $update_new_status = $this->Invoice->queryWithStatus("UPDATE invoices SET status = '" . $new_status . "' WHERE invoice_number= '$invoice_number'");
                }
                //get amount of invoice if it's changed
                $new_amount = $invoice->getTotalAmount();
                $new_amount = $new_amount->value;
                if ($new_amount != $old_amount) {
                    $update_new_amount = $this->Invoice->queryWithStatus("UPDATE registrations SET amount = " . $new_amount . " WHERE id=" . $reg_id);
                }
                if ($new_status != 'SENT') {
                    if ($new_status == 'PAID' || $new_status == 'MARKED_AS_PAID') {
                        $response['message'] =  'Invoice number: '. $invoice->number .' is already fully paid. Can\'t edit. ';
                        _json_echo('admin_editInvoice', $response);
                        return;
                    }
                    $paid_amount = $invoice->getPaidAmount();
                    if ($paid_amount) {
                        if (isset($paid_amount->other)) {
                            $paid_amount_other = (float) $paid_amount->other->value;
                        } else {
                            $paid_amount_other = 0;
                        }
                        if (isset($paid_amount->paypal)) {
                            $paid_amount_paypal = (float) $paid_amount->paypal->value;
                        } else {
                            $paid_amount_paypal = 0;
                        }
                        if ( ($paid_amount_other + $paid_amount_paypal) == $new_amount ) {
                            $response['message'] =  'Invoice number: '. $invoice->number .' is already fully paid. Can\'t edit. ';
                            _json_echo('admin_editInvoice', $response);
                            return;
                        }
                        if (($paid_amount_other + $paid_amount_paypal) > $edit_amount) {
                            $paid_amount_number = $paid_amount_other + $paid_amount_paypal;
                            $response['message'] =  'Can\'t edit amount lower than total paid amount ($'.$paid_amount_number.' HKD)';
                            _json_echo('admin_editInvoice', $response);
                            return;
                        }
                        // logError('admin_editInvoice - paid_amount: ' . print_r($paid_amount, true));
                    } else {
                        logError('admin_editInvoice - don\'t have yet any paid_amount');
                    }
                }
                // Update invoice with new amount
                $update_invoice = $paypal->updateInvoice($invoice, $edit_amount);
                if ($update_invoice['status'] == 'OK') {
                    $update_invoice_status = $update_invoice['value'];
                    if ($update_invoice_status) {
                        $update_new_amount = $this->Invoice->queryWithStatus("UPDATE registrations SET amount = " . $edit_amount . " WHERE id=" . $reg_id);
                        $response['status'] = 'OK';
                        $response['message'] = 'Successful';
                    } else {
                        $response['message'] = 'Can not update invoice';
                    }
                } else {
                    $response['message'] = $update_invoice['value'];
                }
            } else {
                $response['message'] = 'Can not get invoice';
            }
        } else {
            $response['message'] = 'Invalid data';
        }
        
        _json_echo('admin_editInvoice', $response);
    }

    function admin_testInvoice() {
        
        //get informations
        $invoice_identification = "INV2-VBYC-C58K-KLMX-4YPD";
        
        // $amount = $_POST['data']['keyless']['refund_amount'];
        // $transaction_id = $_POST['data']['keyless']['transaction_id'];
        // $invoice_number = $_POST['data']['keyless']['invoice_number'];
        // $reason = $_POST['data']['keyless']['reason'];
        // $currency = "HKD";

        // get invoice
        require_once(APP_ROOT . '/paypal/app/Paypal.php');
        $paypal = new PayPal();
        $invoice_info = $paypal->getInvoice($invoice_identification);

        // var_dump($incoice_info);

        // error_log(print_r($invoice_info, true));

        if ($invoice_info['status'] == 'ERROR') {
            // some time invoice is no longer valid, display error and just ignore
            $response['message'] = "ERROR - getInvoice";
            // error_log($response['message']);
            // _json_echo('admin_refundPayment', $response);
            // error_log(print_r($response, true));
            return;
        } else {
            // get refund status
            $invoice = $invoice_info['value'];
            $result = $paypal->getInvoiceStatus($invoice);
            if ($result['status'] == 'ERROR') {
                logError('admin_reconcilePayment() - getInvoiceStatus: ' . $result['value']);
                // continue;
            }
            // $new_status = $paypal->getInvoiceStatus($invoice);
            $new_status = $result['value'];
            // $new_status = $invoice->getStatus();
            if ($new_status == '') {
                // error_log('admin_reconcilePayment() - getInvoiceStatus ERROR: ' . $result['value']);
                $response['message'] = "ERROR - getInvoice";
                // error_log($response['message']);
                // _json_echo('admin_refundPayment', $response);
                // error_log(print_r($response, true));
                return;
            } else {
                // record refund
                if ($new_status !== 'REFUNDED') {
                    $infos['invoice_identification'] = $invoice_identification;
                    // logError('admin_checkRefundInvoice - invoice: '. print_r($invoice, true));
            
                    if (isset($invoice->paid_amount->paypal)) {
                        $infos['original_payment'] = $invoice->paid_amount->paypal->value;
                        $payments =  $invoice->getPayments();
                        // error_log('admin_checkRefundInvoice - payments: '. print_r($payments, true));
                        $check_paypal_payment = false;
                        foreach($payments as $payment) {
                            if ($payment->type !== 'PAYPAL') {
                                continue;
                            } else {
                                $check_paypal_payment = true;
                                $transaction_id = $payment->transaction_id;
                                $infos['transaction_id'] = $transaction_id;
                                // Check refunded amount:
                                $refunded_amount = $paypal->getRefundedAmount($transaction_id);
                                // error_log(print_r($refunded_amount, true));
                                if ($refunded_amount['status'] == 'OK') {
                                    if($infos['original_payment'] != $refunded_amount['value']) {
                                        $infos['amount_remaining'] = $infos['original_payment'] - $refunded_amount['value'];
                                    } else {
                                        $response['message'] = "This invoice has been fully refunded!";
                                        // _json_echo('admin_recordPayment', $response);
                                        // error_log(print_r($response, true));
                                        return;
                                    }
                                } else {
                                    $response['message'] = "Can't get refunded amount of this invoice";
                                    // _json_echo('admin_recordPayment', $response);
                                    // error_log(print_r($response, true));
                                    return;
                                }
                                break;
                            }
                        }
                        if ($check_paypal_payment) {
                            $response['status'] = 'OK';
                            $response['data'] = $infos;
                        } else {
                            $response['message'] = "This invoice can't be refunded because it don't have any payments via paypal";
                        }
                    } else {
                        $response['message'] = "This invoice can't be refunded because it don't have any payments via paypal";
                    }
                } else {
                    $response['message'] = "This invoice has been refunded!";
                    // $result = $this->Invoice->queryWithStatus("UPDATE invoices SET status = '" . $new_status . "' WHERE invoice_identification= '" . $invoice_identification."'");
                    // _json_echo('admin_recordPayment', $response);
                    // error_log(print_r($response, true));
                    return;
    
                }

            }
        }
        // error_log(print_r($response, true));
        // _json_echo('admin_refundPayment', $response);
    }

    function testRefund() {
        $captureId = '19638097NU999572S';
        $value = '20.00';
        $currency_code = 'HKD';

        require_once(APP_ROOT . '/paypal/app/Paypal_v2.php');
        $paypal = new PayPal();
        $paypal->refundOrder($captureId, $value, $currency_code);
    }

    function createOrder() {
        require_once(APP_ROOT . '/paypal/app/Paypal_v2.php');
        $paypal = new PayPal();
        $paypal->createOrder();
    }

    function getWebhooks() {
        require_once(APP_ROOT . '/paypal/app/Paypal.php');
        $paypal = new PayPal();

        $response = $paypal->getWebhooks();
        _json_echo('getWebhooks', $response);
    }

    function createWebhook() {
        require_once(APP_ROOT . '/paypal/app/Paypal.php');
        $paypal = new PayPal();

        $url = SERVER_PATH . "invoices/listenWebhookPayPal";
        error_log('createWebhook - url: ' . $url);
        $event_types = [
            [
                "name" => "INVOICING.INVOICE.CANCELLED"
            ],
            [
                "name" => "INVOICING.INVOICE.CREATED"
            ],
            [
                "name" => "INVOICING.INVOICE.PAID"
            ],
            [
                "name" => "INVOICING.INVOICE.REFUNDED"
            ],
            [
                "name" => "INVOICING.INVOICE.SCHEDULED"
            ],
            [
                "name" => "INVOICING.INVOICE.UPDATED"
            ]
        ];

        $webhook = $paypal->createWebhook($url, $event_types);
        
        _json_echo('createWebhook', $webhook);
    }

    function listenWebhookPayPal()
    {
    	error_log("listenWebhookPayPal");
        $payload = @file_get_contents('php://input');
        $data = json_decode($payload);
        error_log("listenWebhookPayPal - data: " . json_encode($data));
        $event_type = $data->event_type;

        // Handle the event
        switch ($event_type) {
            case 'INVOICING.INVOICE.CANCELLED':
                $result = $this->autoUpdatePaymentStatus($data);
                break;
            case 'INVOICING.INVOICE.CREATED':
                $result = $this->autoUpdatePaymentStatus($data);
                break;
            case 'INVOICING.INVOICE.PAID':
                $result = $this->autoUpdatePaymentStatus($data);
                break;
            case 'INVOICING.INVOICE.REFUNDED':
                $result = $this->autoUpdatePaymentStatus($data);
                break;
            case 'INVOICING.INVOICE.SCHEDULED':
                $result = $this->autoUpdatePaymentStatus($data);
                break;
            case 'INVOICING.INVOICE.UPDATED':
                $result = $this->autoUpdatePaymentStatus($data);
                break;
            default:
                $result = false;
        }
        
        if ($result) {
            http_response_code(200);
        } else {
            http_response_code(400);
        }
    }

    private function autoUpdatePaymentStatus($data)
    {
        // get invoice id and status from std class
        $id= '';
        $status = '';

        $id = $data->resource->invoice->id;
        $status = $data->resource->invoice->status;

        error_log("autoUpdatePaymentStatus - id: $id, status: $status");

        // check if invoice is exist
        $invoice = $this->Invoice->queryWithOneResultSet("SELECT * FROM invoices WHERE invoice_identification = '$id' LIMIT 1");
        if ($invoice) {
            // update invoice status
            $this->Invoice->queryWithStatus("UPDATE invoices SET status = '" . $status . "' WHERE invoice_identification = '" . $id . "'");

            // if invoice = PAID, then update 'team bus' flag to 'yes'
            if ($status == 'PAID' || $status == 'MARKED_AS_PAID') {
                $this->updateTeamBus($invoice['registration_id'], $invoice['meta_data']);
            }
        } else {
            error_log("autoUpdatePaymentStatus - invoice not found - ID = $id");
        }

        return false;
    }

    private function updateTeamBus($registration_id, $meta_data) {
        error_log('updateTeamBus - registration_id: ' . $registration_id . ', meta_data: ' . $meta_data);
        // check registration enroll team bus when register event (registration_id) or respond to match invitation (meta_data)
        if (!empty($meta_data)) {
            // convert string to json
            $meta_data = json_decode($meta_data, true);
            $registration_id = $meta_data['registration_id'];
        } elseif (!empty($registration_id)) {
            $registration_id = $registration_id;
            // check team_bus in registrations table. No need to update if team_bus = 0
            $registration = $this->Invoice->queryWithOneResultSet("SELECT team_bus FROM registrations WHERE id = '$registration_id' AND team_bus = 1");
            if (!$registration) {
                $registration_id = 0;
            }
        } else {
            error_log('updateTeamBus - no registration_id or meta_data');
            return;
        }

        // get player_id, event_id and team_bus in registration
        $registration = $this->Invoice->queryWithOneResultSet("SELECT player_id, event_id, team_bus FROM registrations WHERE id = '$registration_id'");

        if (!$registration) {
            error_log('updateTeamBus - registration not found - ID = ' . $registration_id);
            return;
        }

        $player_id = $registration['player_id'];
        $event_id = $registration['event_id'];

        // get pgroup_players
        $pgroup_players = $this->Invoice->queryWithResultSet("SELECT * FROM pgroup_players WHERE player_id = '$player_id' AND pgroup_id IN (SELECT id FROM pgroups WHERE event_id = $event_id)");

        if ($pgroup_players['status'] == 'OK') {
            $pgroup_players = $pgroup_players['info']['rows'];
            error_log('updateTeamBus - found ' . count($pgroup_players) . ' pgroup_player(s)');
    
            // get pgroup_player_ids
            $pgroup_player_ids = array_column($pgroup_players, 'id');
    
            if (count($pgroup_player_ids) > 0) {
                $pgroup_player_ids = implode(',', $pgroup_player_ids);
                error_log('updateTeamBus - pgroup_player_ids: ' . $pgroup_player_ids);
                // update in_team_bus value
                $update = $this->Invoice->queryWithStatus("UPDATE pgroup_players SET in_team_bus = 1 WHERE player_id = '$player_id' AND id IN ($pgroup_player_ids)");
                // update team_bus value in registrations
                $res = $this->Invoice->queryWithStatus("UPDATE registrations SET team_bus = 1 WHERE id = '$registration_id'");
            } else {
                error_log('updateTeamBus - no pgroup_player found');
                return;
            }
        }
    }

    /**
     * Cron job auto update payment status of invoices
     */
    public function cronjob_updateInvoiceStatus() {
        error_log('cronjob_updateInvoiceStatus');

        // get all bus team invoices
        $invoices = $this->Invoice->queryWithResultSet("SELECT * FROM invoices WHERE status = 'SENT' AND invoice_number LIKE '%BUS'");
        if ($invoices['status'] == "OK") {
            $invoices = $invoices['info']['rows'];
            error_log('cronjob_updateInvoiceStatus - found ' . count($invoices) . ' invoice(s)');
            // set up paypal and mail
            require_once(APP_ROOT . '/paypal/app/Paypal.php');
            $paypal = new PayPal();

            $total_invoice = count($invoices);
            for ($i=0; $i < $total_invoice; $i++) { 
                // get invoice payment
                $invoice = $invoices[$i];
                $invoice_id = $invoice['id'];
                $status = $invoice['status'];
                $result = $paypal->getInvoice($invoice['invoice_identification']);
                if ($result['status'] == 'ERROR') {
                    // some time invoice is no longer valid, display error and just ignore
                    error_log('admin_reconcilePayment() - getInvoice: ' . $result['value']);
                    continue;
                }

                // get payment status
                $inv = $result['value'];
                $new_status = $inv->getStatus();

                if ($new_status != $status) {
                    error_log("cronjob_updateInvoiceStatus - invoice $invocie_id status changed from $status to $new_status");
                    // update invoice status
                    $this->Invoice->queryWithStatus("UPDATE invoices SET `status` = '" . $new_status . "' WHERE id = $invoice_id");

                    // if invoice = PAID, then update 'team bus' flag to 'yes'
                    if ($new_status == 'PAID' || $new_status == 'MARKED_AS_PAID') {
                        $this->updateTeamBus($invoice['registration_id'], $invoice['meta_data']);
                    }
                } else {
                    error_log("cronjob_updateInvoiceStatus - invoice $invocie_id status not changed");
                }
            }
        }
    }


    public function testPaypalServer()
    {

        try {
            $response = [
                "status" => "ERROR",
                "message" => null
            ];

            if (!isset($_POST['invoice_id']) || empty($_POST['invoice_id'])) {
                $response['message'] = "Missing invoice_id";
                return _json_echo("testPaypalServer", $response);
            }

            $invoiceId = $_POST['invoice_id'];

            require_once(FRAMEWORK_ROOT . '/payment/lib/PaypalServerHelper/PaypalServerApi.php');
            $paypalInstance = new PaypalServerApi();

            $invoiceInfo = $paypalInstance->getInvoiceInfo("test");

            // return _json_echo("testPaypalServer", ["status" => "ok"]);
            return _json_echo("testPaypalServer", $invoiceInfo);
        } catch (Exception $error) {
            return _json_echo("testPaypalServer", [
                "status" => "ERROR",
                "message" => $error->getMessage()
            ]);
        }
    }


	function afterAction() {
	
	}
}