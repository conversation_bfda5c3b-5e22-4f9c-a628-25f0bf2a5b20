<?php
    /*
    *   PayPalUtil
    *   Author: <PERSON><PERSON>
    *   Date: 2015-05-10
    *   Purpose: To handle all things related to paypal 
    */ 
     
    require FRAMEWORK_ROOT . '/vendor/autoload.php';

    use PayPal\Rest\ApiContext;
    use PayPal\Auth\OAuthTokenCredential;
    use PayPal\Api\Invoice;
    use PayPal\Api\MerchantInfo;
    use PayPal\Api\BillingInfo;
    use PayPal\Api\InvoiceItem;
    use PayPal\Api\Phone;
    use PayPal\Api\Address;
    use PayPal\Api\Currency;
    use PayPal\Api\PaymentTerm;
    use PayPal\Api\ShippingInfo;
    use PayPal\Api\InvoiceAddress;
    use PayPal\Api\Notification;
    use PayPal\Api\PaymentDetail;
    use PayPal\Api\Refund;
    use PayPal\Api\Sale;
    use PayPal\Api\Amount;

    /**
     * <AUTHOR> Quyet Tien
     * @since 25/05/2019
     * @todo Update PayPal API Version 2
     */
    use PayPalCheckoutSdk\Core\PayPalHttpClient;
    use PayPalCheckoutSdk\Core\SandboxEnvironment;
    use PayPalCheckoutSdk\Core\ProductionEnvironment;

    use Sample\PayPalClient;
    use PayPalCheckoutSdk\Orders\OrdersCreateRequest;
    use PayPalCheckoutSdk\Payments\CapturesRefundRequest;
    
    class Paypal {

        private $apiContext;
        private $client;

        function __construct() {
            $this->apiContext = $this->getApiContext();    
        }
        
        function getApiContext() {

            if (PAYPAL_LIVE) {
                $clientId = LIVE_CLIENT_ID;
                $clientSecret = LIVE_CLIENT_SECRET;
                $config = array(
                    'mode' => 'live',
                    'log.LogLevel' =>'INFO',
                    'log.LogEnabled' => true,
                    'log.FileName' => '../../paypal/PayPal.log',
                    'validation.level' => 'log',
                    'cache.enabled' => true,
                    );
                $environment = new ProductionEnvironment(LIVE_CLIENT_ID, LIVE_CLIENT_SECRET);
            } else {
                $clientId = SANDBOX_CLIENT_ID;
                $clientSecret = SANDBOX_CLIENT_SECRET;
                $config = array(
                    'mode' => 'sandbox',
                    'log.LogLevel' =>'DEBUG',
                    'log.LogEnabled' => true,
                    'log.FileName' => '../../paypal/PayPal.log',
                    'validation.level' => 'log',
                    'cache.enabled' => true,
                    );
                $environment = new SandBoxEnvironment(SANDBOX_CLIENT_ID, SANDBOX_CLIENT_SECRET);
            }

            $this->client = new PayPalHttpClient($environment);

            $this->apiContext = new ApiContext(new OAuthTokenCredential($clientId, $clientSecret));
            $this->apiContext->setConfig($config);
            return $this->apiContext;
        }
        
        function getInvoice($invoiceId) {
            try {
                $invoice = new Invoice();
                $invoice = $invoice->get($invoiceId, $this->apiContext);
                return array('status' => 'OK', 'value' => $invoice);
                // return $invoice;
            } catch (Exception $ex) {
                // error_log('Paypal.getInvoice() - Exception: ' . $ex->getMessage());
                return array('status' => 'ERROR', 'value' => 'ERROR - PAYPAL - ' . $ex->getMessage());
                // return null;
            }
        }
        
        function listInvoices() {
            try {
                $invoices = Invoice::getAll(array('page' => 0, 'page_size' => 4, 'total_count_required' => 'true'), $this->apiContext);
                return array('status' => 'OK', 'value' => $invoices);
                // return $invoices;
            } catch (Exception $ex) {
                // error_log('Paypal.listInvoices() - Exception: ' . $ex->getMessage());
                return array('status' => 'ERROR', 'value' => 'ERROR - PAYPAL - ' . $ex->getMessage());
                // return null;
            }
        }
        
        function createInvoice($number, $billingInfo, $itemInfo) {

            $invoice = new Invoice();
            $invoice->setNumber($number);

            if (PAYPAL_LIVE) {
                $receiverEmail =  trim($billingInfo['email1']);
                $account = LIVE_ACCOUNT;
            } else {
                $receiverEmail = MAIL_TEST_ACCOUNT;
                $account = SANDBOX_ACCOUNT;
            }

            // Merchant info
            $invoice->setMerchantInfo(new MerchantInfo())
            ->setBillingInfo(array(new BillingInfo()))
            ->setNote('HKFC Invoice '. date('d-M-Y'))
            ->setPaymentTerm(new PaymentTerm())
            ->setShippingInfo(new ShippingInfo());

            $invoice->getMerchantInfo()
            ->setEmail($account)
            ->setFirstName('HKFC')
            ->setLastName('Junior Soccer')
            ->setBusinessName('Hong Kong Football Club')
            ->setAddress(new Address());

            $invoice->getMerchantInfo()->getAddress()
            ->setLine1('3 Sports Road')
            ->setCity('Happy Valley')
            ->setState('Hong Kong')
            ->setCountryCode('HK');

            // Billing info
            $billing = $invoice->getBillingInfo();
            $billing[0]->setEmail($receiverEmail);
            $billing[0]->setBusinessName($billingInfo['name1'])
            ->setAddress(new InvoiceAddress());

            $billing[0]->getAddress()
            ->setLine1('Hong Kong')
            ->setCity('Hong Kong')
            ->setState('Hong Kong')
            ->setCountryCode('HK');

            // Shipping info
            $invoice->getShippingInfo()
            ->setBusinessName($billingInfo['name1'])
            ->setAddress(new InvoiceAddress());

            $invoice->getShippingInfo()->getAddress()
            ->setLine1('Hong Kong')
            ->setCity('Hong Kong')
            ->setState('Hong Kong')
            ->setCountryCode('HK');
            
            // Item info
            $items = array();
            $items[0] = new InvoiceItem();
            $items[0]->setName($itemInfo['first_name']. ' '. $itemInfo['last_name'] . ' in Season ' . $itemInfo['event'])
            ->setQuantity(1)
            ->setUnitPrice(new Currency());

            $items[0]->getUnitPrice()
            ->setCurrency($itemInfo['currency'])
            ->setValue($itemInfo['price']);

            $invoice->setItems($items);
            $invoice->getPaymentTerm()->setTermType('DUE_ON_RECEIPT');

            // Logo
            $invoice->setLogoUrl('https://www.hkfc.com.hk/sites/hkfc/themes/sport/logo.png');

            try {
                $invoice->create($this->apiContext);
                // error_log('Paypal.createInvoice() - Invoice status: ' . $invoice->getStatus());
                return array('status' => 'OK', 'value' => $invoice);
                // return $invoice;
            } catch (Exception $ex) {
                return array('status' => 'ERROR', 'value' => 'ERROR - PAYPAL - ' . $ex->getMessage());
                // error_log('Paypal.createInvoice() - Exception: ' . $ex->getMessage());
                // return null;
            }
        }

        function sendInvoice($invoice) {
            try {   
                $send = $invoice->send($this->apiContext);
                $sent_invoice = $this->getInvoice($invoice->getId());
                if ($sent_invoice['status'] == 'OK') {
                    $sent_invoice = $sent_invoice['value'];
                } else {
                    return array('status' => 'ERROR', 'value' => $sent_invoice['value']);
                }
                // error_log('Paypal.sendInvoice() - Send status: ' . $sendStatus);
                return array('status' => 'OK', 'value' => $sent_invoice);
                // return $sendStatus; 
            } catch (Exception $ex) {
                // error_log('Paypal.sendInvoice() - Exception: ' . $ex->getMessage());
                return array('status' => 'ERROR', 'value' => 'ERROR - PAYPAL - ' . $ex->getMessage());
                // return null;
            }
        }
        
        function remindInvoice($invoice, $subject, $note, $merchant) {
            try {
                $notify = new Notification();
                $notify->setSubject($subject)
                    ->setNote($note)
                    ->setSendToMerchant($merchant);

                $remindStatus = $invoice->remind($notify, $this->apiContext);
                // error_log('Paypal.remindInvoice() - Remind status: ' . $remindStatus);

                return array('status' => 'OK', 'value' => $remindStatus);
                // return $remindStatus;
            } catch (Exception $ex) {
                // error_log('Paypal.remindInvoice() - Exception: ' . $ex->getMessage());
                return array('status' => 'ERROR', 'value' => 'ERROR - PAYPAL - ' . $ex->getMessage());
                 // return null;
            }    
        }

        function getInvoiceStatus($invoice) {
            try {
                $status = $invoice->getStatus();
                return array('status' => 'OK', 'value' => $status);
                // error_log('Paypal.getInvoiceStatus() - Invoice status: ' . $status);
                // return $status;
            } catch (Exception $ex) {
                // error_log('Paypal.getInvoiceStatus() - Exception: ' . $ex->getMessage());
                return array('status' => 'ERROR', 'value' => 'ERROR - PAYPAL - ' . $ex->getMessage());
                // return null;
            }    
        }

        function getInvoicePayments($invoice) {
            try {
                $details = $invoice->getPayments();
                return array('status' => 'OK', 'value' => $details);
                // error_log('Paypal.getInvoicePayments() - Invoice payment detail: ' . $details);
                // return $details;
            } catch (Exception $ex) {
                return array('status' => 'ERROR', 'value' => 'ERROR - PAYPAL - ' . $ex->getMessage());
                // error_log('Paypal.getInvoicePayments() - Exception: ' . $ex->getMessage());
                // return null;
            }    
        }
        
        function getInvoiceNumber($invoice) {
            try {
                $result = $invoice->getNumber();

                return array('status' => 'OK', 'value' => $result);

            } catch (Exception $ex) {
                return array('status' => 'ERROR', 'value' => 'ERROR - PAYPAL - ' . $ex->getMessage());
            }    
        }
        
        /**
         * Le Quyet Tien
         * 05/12/2018
         * https://developer.paypal.com/docs/api/invoicing/v1/#invoices_record-payment
         */
        function recordPayment($invoice, $method, $date, $note, $amount, $currency) {
            try {
                $currency_info = new Currency();
                $currency_info
                    ->setCurrency($currency)
                    ->setValue($amount);

                $record = new PaymentDetail();
                $record
                    ->setMethod($method)
                    ->setDate($date)
                    ->setNote($note)
                    ->setAmount($currency_info);
                
                $recordStatus = $invoice->recordPayment($record, $this->apiContext);
                
                return array('status' => 'OK', 'value' => $recordStatus);
            } catch (Exception $ex) {
                return array('status' => 'ERROR', 'value' => 'ERROR - recordPayment - ' . $ex->getMessage());
            }
        }

        function getAmountAndCurrency($invoice) {
            try {
                $invoice->getTotalAmount(new Currency());
                $value = $invoice->getTotalAmount()
                    ->getValue();
                $currency = $invoice->getTotalAmount()
                    ->getCurrency();
                return array('status' => 'OK', 'value' => array('value' => $value, 'currency' => $currency));
            } catch (Exception $ex) {
                return array('status' => 'ERROR', 'value' => 'ERROR - PAYPAL - ' . $ex->getMessage());
                // error_log('Paypal.getInvoicePayments() - Exception: ' . $ex->getMessage());
                // return null;
            }    
        }

        /**
         * Le Quang Thanh
         * 09/12/2018
         * https://developer.paypal.com/docs/api/invoicing/v1/#invoices_record-refund
         */
        function refund($transaction_id, $reason, $amount, $currency) {
            try {
                $amount_info = new Amount();
                $amount_info
                    ->setCurrency($currency)
                    ->setTotal($amount);

                $refund_info = new Refund();
                $refund_info
                    ->setReason($reason)
                    ->setAmount($amount_info);
                // logError('Paypal - refund: '. print_r($refund_info, true));
                $sale = new Sale();
                $sale->setId($transaction_id);
                $refund = $sale->refund($refund_info, $this->apiContext);
                
                return array('status' => 'OK', 'value' => $refund);
            // } catch (PayPal\Exception\PayPalConnectionException $ex) {
            //     echo $ex->getCode(); // Prints the Error Code
            //     echo $ex->getData(); // Prints the detailed error message 
            //     die($ex);
            } catch (Exception $ex) {
                return array('status' => 'ERROR', 'value' => 'ERROR - recordPayment - ' . $ex->getMessage());
            }
        }

        function getRefundedAmount($transaction_id) {
            logError('Paypal - Payment: ' . $transaction_id);
            try {
                $refund = new Refund();
                $result = $refund->get($transaction_id, $this->apiContext);
                $refunded_amount = $result->total_refunded_amount['value'];
                return array('status' => 'OK', 'value' => $refunded_amount);
            } catch (Exception $ex) {
                return array('status' => 'ERROR', 'value' => 'ERROR - getRefundedAmount - ' . $ex->getMessage());
            }
        }

        function updateInvoice($invoice, $amount) {
            $item_info = $invoice->getItems();
            $item_info = $item_info[0];
           // Item info
            $items = array();
            $items[0] = new InvoiceItem();
            $items[0]->setName($item_info->name)
            ->setQuantity(1)
            ->setUnitPrice(new Currency());
           
            $items[0]->getUnitPrice()
            ->setCurrency('HKD')
            ->setValue($amount);
           
            $invoice->setItems($items);
            $item_info = $invoice->getItems();
            $item_info = $item_info[0];
            unset($invoice->payments);
            try {
                // logError('Paypal - updateInvoice - invoice: '. print_r($invoice, true));
                $update = $invoice->update($this->apiContext);
                return array('status' => 'OK', 'value' => $update);
            } catch (Exception $ex) {
                return array('status' => 'ERROR', 'value' => 'ERROR - updateInvoice - ' . $ex->getMessage());
            }
        }

        function refundOrder($captureId, $amount, $currency) {
            $request = new CapturesRefundRequest($captureId);
            $request->body = self::buildRequestBody($amount, $currency);
            // 3. Call PayPal to refund a capture
            $client = PayPalClient::client();
            $response = $client->execute($request);

            if (PAYPAL_SANDBOX) {
                print "Status Code: {$response->statusCode}\n";
                print "Status: {$response->result->status}\n";
                print "Order ID: {$response->result->id}\n";
                print "Links:\n";
                foreach($response->result->links as $link)
                {
                    print "\t{$link->rel}: {$link->href}\tCall Type: {$link->method}\n";
                }
                // To toggle printing the whole response body comment/uncomment
                // the following line
                echo json_encode($response->result, JSON_PRETTY_PRINT), "\n";
            }
            return $response;
        }

        public static function buildRequestBody($amount, $currency)
        {
            return array(
                'amount' =>
                    array(
                        'value' => $amount,
                        'currency_code' => $currency
                    )
            );
        }

        function createOrder() {
            $request = new OrdersCreateRequest();
            $request->prefer('return=representation');
            $request->body = [
                                "intent" => "CAPTURE",
                                "purchase_units" => [[
                                    "reference_id" => "test_ref_id1",
                                    "amount" => [
                                        "value" => "100.00",
                                        "currency_code" => "USD"
                                    ]
                                ]],
                                "application_context" => [
                                    "cancel_url" => "https://example.com/cancel",
                                    "return_url" => "https://example.com/return"
                                ] 
                            ];

            try {
                // Call API with your client and get a response for your call
                $response = $this->client->execute($request);
                
                // If call returns body in response, you can get the deserialized version from the result attribute of the response
                print_r($response);
            }catch (HttpException $ex) {
                echo $ex->statusCode;
                print_r($ex->getMessage());
            }
        }
    }
?>