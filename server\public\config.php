<?php

/** Configuration Variables **/

$url_temp = 'http' . (isset($_SERVER['HTTPS']) ? 's' : '') . '://' . "{$_SERVER['HTTP_HOST']}/{$_SERVER['REQUEST_URI']}";

if (strpos($url_temp, 'localhost') || strpos($url_temp, 'ngrok')) {
	// localhost mode
	// require_once (SERVER_ROOT . DS . 'config' . DS . 'configLocal.php');
	define('PRODUCTION_ENVIRONMENT', false);
	define('DEVELOPMENT_ENVIRONMENT', true);
	define('DEBUG_ENVIRONMENT', false);
	define('MAINTENANCE_ENVIRONMENT', false);

	define('PAYPAL_LIVE', false);
	define('PAYPAL_SANDBOX', true);

	define('MAIL_LIVE', false);
	define('MAIL_TEST', true);
	define('MAIL_TEST_ACCOUNT', '<EMAIL>');
	define('PAYPAL_TEST_ACCOUNT', '<EMAIL>');

	define('PUSH_LIVE', false);
	define('PUSH_TEST', true);
	define('PUSH_TEST_DEVICE', 'duCK_2NNS0HtsLfJb1Yj2s:APA91bH2batOkI-6OWBHM7RrTJZMV8sfrofuEkcKkz5EYFTCVECHqTfnDU0LJ_2bQgQkZzjYtRzVTU1ma-_RsGJyT85noKkUUk5B5M7099dXCrYi9LTabw0');

	define('DB_NAME', 'hkfcjuni_enroll');
	define('DB_USER', 'root');
	define('DB_PASSWORD', '');
	define('DB_HOST', 'localhost');

	define('TEST_MODE', false);

	define('DEFAULT_TIMEZONE', 'Asia/Ho_Chi_Minh');

	define('APP_PATH', 'http://localhost/ezactivevn/hkfc/');

	define('SMTP_HOST', 'mail.ezactive.com');
	define('SMTP_USERNAME', '<EMAIL>');
	define('SMTP_PASSWORD', 'b^)&H=%6--KK');
	define('SMTP_FROM_MAIL', '<EMAIL>');

	define('SENTRY_DSN', 'https://<EMAIL>/****************');
	define('SENTRY_ENABLED', true);
	define('SENTRY_ENVIRONMENT', 'development');
} else if (strpos($url_temp, 'staging')) {
	// staging environment
	define('PRODUCTION_ENVIRONMENT', false);
	define('DEVELOPMENT_ENVIRONMENT', true);
	define('DEBUG_ENVIRONMENT', false);
	define('MAINTENANCE_ENVIRONMENT', false);

	define('PAYPAL_LIVE', false);
	define('PAYPAL_SANDBOX', true);

	define('MAIL_LIVE', false);
	define('MAIL_TEST', true);
	define('MAIL_TEST_ACCOUNT', '<EMAIL>');
	define('PAYPAL_TEST_ACCOUNT', '<EMAIL>');

	define('PUSH_LIVE', false);
	define('PUSH_TEST', true);
	define('PUSH_TEST_DEVICE', 'c_GbxoY8-OI:APA91bHxbj-rZLfv-CtXz6GigSN378hUifvhMXSFBjQZzXZad7ucfhBhaWJ90QKm-aR3qKsUCtH_yqL9sbTfo6bW00Ujvc7k6C4hr_QpSOZlaZCcyL6vnhfUnl4F9rnuPJfAi2-anV_t');

	define('DB_NAME', 'hkfcjuni_enroll_staging');
	define('DB_USER', 'hkfcjuni_enroll');
	define('DB_PASSWORD', 'wT31s5R&r7fF');
	define('DB_HOST', 'localhost');

	define('TEST_MODE', false);

	define('DEFAULT_TIMEZONE', 'Asia/Hong_Kong');

	define('APP_PATH', 'https://www.hkfcjuniorsoccer.com/admin_panel/staging/hkfc/');

	define('SMTP_HOST', 'mail.hkfcjuniorsoccer.com');
	define('SMTP_USERNAME', '<EMAIL>');
	define('SMTP_PASSWORD', 'jh2x@h)Sk*X,');
	define('SMTP_FROM_MAIL', '<EMAIL>');

	define('SENTRY_DSN', 'https://<EMAIL>/****************');
	define('SENTRY_ENABLED', true);
	define('SENTRY_ENVIRONMENT', 'staging');
} else {
	// webhost mode
	// require_once (SERVER_ROOT . DS . 'config' . DS . 'configServer.php');
	define('PRODUCTION_ENVIRONMENT', true);
	define('DEVELOPMENT_ENVIRONMENT', true);
	define('DEBUG_ENVIRONMENT', false);
	define('MAINTENANCE_ENVIRONMENT', false);

	define('PAYPAL_LIVE', true);
	define('PAYPAL_SANDBOX', false);

	define('MAIL_LIVE', true);
	define('MAIL_TEST', false);
	define('MAIL_TEST_ACCOUNT', '<EMAIL>');
	define('PAYPAL_TEST_ACCOUNT', '<EMAIL>');

	define('PUSH_LIVE', true);
	define('PUSH_TEST', false);

	define('DB_NAME', 'hkfcjuni_enroll');
	define('DB_USER', 'hkfcjuni_enroll');
	define('DB_PASSWORD', 'wT31s5R&r7fF');
	define('DB_HOST', 'localhost');

	define('DEFAULT_TIMEZONE', 'Asia/Hong_Kong');

	define('TEST_MODE', false);

	define('APP_PATH', 'https://www.hkfcjuniorsoccer.com/admin_panel/hkfc/');

	define('SMTP_HOST', 'mail.hkfcjuniorsoccer.com');
	define('SMTP_USERNAME', '<EMAIL>');
	define('SMTP_PASSWORD', '&N3Gr{RM973#');
	define('SMTP_FROM_MAIL', '<EMAIL>');

	define('SENTRY_DSN', 'https://<EMAIL>/****************');
	define('SENTRY_ENABLED', true);
	define('SENTRY_ENVIRONMENT', 'production');
}

define('APP_NAME', 'HKFC Junior Soccer');

define('SMTP_PORT', 465);
define('SMTP_SECURE', true);
define('SMTP_UTF8', true);
define('SMTP_FROM_NAME', 'HKFC Junior Soccer');
define('SMTP_FRONTDESK_MAIL', '<EMAIL>');
define('SMTP_FRONTDESK_NAME', 'HKFC - Front Desk');
define('SMTP_ADMIN_PANEL_MAIL', '<EMAIL>');
define('SMTP_ADMIN_PANEL_NAME', 'HKFC Junior Soccer');
define('SMTP_APP_SUPPORT_MAIL', '<EMAIL>');
define('SMTP_APP_SUPPORT_NAME', 'HKFC Junior Soccer - App Support');

// Firebase Cloud Message
#API access key from Google API's Console
define('API_ACCESS_KEY', 'AAAA6OAX8sk:APA91bF3JxOHJkcQ2IGLzZgCvip6O9adQw51afssGNMsAT0FFjCjOMLDrh8_40SHFQjywVeVuuyiD7l7CNiL_cFJeXk_estyWHlXu3laGL46s8dvHTU9-tQ_Yt1Ra3U5V_kSa59AbiDmndJAzEZezUOlAEwBEQ2tpw');
define('SENDER_ID', '1000192078537');


define('SERVER_PATH', APP_PATH . 'server/');
define('SYSTEM_IMAGE_PATH', APP_ROOT . DS . 'images' . DS . 'system' . DS);
define('PRODUCT_IMAGE_PATH', APP_ROOT . DS . 'images' . DS . 'product' . DS);

define('ERROR_LOG_FILE', APP_ROOT . DS . 'server' . DS . 'error.log');

define('PAGINATE_LIMIT', '5');
define('SIZE_LIMIT', 10 * 1024 * 1024);


// DEFINE CONSTANTS
define('APPROVAL_STATUS_Rollover', 'Rollover');
define('APPROVAL_STATUS_Roll_Invited', 'Roll/Invited');
define('APPROVAL_STATUS_Roll_Register', 'Roll/Register');
define('APPROVAL_STATUS_Roll_Withdraw', 'Roll/Withdraw');
define('APPROVAL_STATUS_Register', 'Register');
define('APPROVAL_STATUS_Waitlist', 'Waitlist');
define('APPROVAL_STATUS_Withdraw', 'Withdraw');
define('APPROVAL_STATUS_Approve', 'Approve');
define('APPROVAL_STATUS_App_Int_Mail', 'App/Inv/Mail');
define('APPROVAL_STATUS_App_Int_NotMail', 'App/Inv/Not Mail');


define('USER_PARENT', 1);
define('USER_COACH', 2);
define('USER_HEAD_COACH', 3);
define('USER_COACH_ADMINISTRATOR', 4);
define('USER_HEAD_COACH_ADMINISTRATOR', 5);
define('USER_CO_HEAD_COACH', 6);
define('USER_PLAYER', 7);
define('USER_TECHNICAL_COACH', 8);
define('USER_ADMINISTRATOR', 9);

define('VOLUNTEER_NO', 0);
define('VOLUNTEER_PARENT_1', 1);
define('VOLUNTEER_PARENT_2', 2);
define('VOLUNTEER_PARENT_3', 3);

define('IS_HEAD_COACH', 1);
define('IS_CO_HEAD_COACH', 2);
define('IS_TECHNICAL_COACH', 3);
define('IS_PARTICIPATING_COACH', 0);

define('EVENT_SPECIAL', 'Special');
define('EVENT_SEASON', 'Season');
define('EVENT_ACTIVE', 'Active');
define('EVENT_ARCHIVED', 'Archived');
define('REG_DATE_ALL', 'all');
define('REG_DATE_EXIST', 'exist');
define('REG_DATE_NEW', 'new');
define('EVENT_FOR_ALL', 0);
define('EVENT_FOR_SEASON_ONLY', 1);
define('EVENT_FOR_YPL_ONLY', 2);
define('EVENT_FOR_SEASON_YPL_ONLY', 3);
define('EVENT_FOR_MIX', 'mix');
define('EVENT_FOR_MALE', 'male');
define('EVENT_FOR_FEMALE', 'female');


define('MIXED_TEAM', 'Mixed Team');
define('GIRL_TEAM', 'Girl Team');
define('COACH_TEAM', 'Coaches Team');
define('SPECIAL_TEAM', 'Special');
define('OTHERS_TEAM', 'Others');
define('YOUTH_PATHWAY', 'Youth Pathway');
define('LUCKY_MILE', 'Lucky Mile');

define('MATCH_PLAYER', 'Match Player');
define('EVENT_PLAYER', 'Event Player');
define('TRAINING_PLAYER', 'Training Player');
define('MATCH_COACH', 'Match Coach');
define('EVENT_COACH', 'Event Coach');

define('STATUS_INVITED', 'INVITED');
define('STATUS_ACCEPTED', 'ACCEPTED');
define('STATUS_RESERVE', 'RESERVE');
define('STATUS_DECLINED', 'DECLINED');
define('STATUS_MAYBE', 'MAYBE');

define('STATUS_NOTI_UNREAD', 0);
define('STATUS_NOTI_READ', 1);
define('STATUS_NOTI_ARCHIVE', 2);

define('SESSION_PRIVATE', 0);
define('SESSION_PUBLIC', 1);

define('EVENT_NOT_ALLOW_REGISTER', 0);
define('EVENT_ALLOW_REGISTER', 1);

define('SEASON_MAIN', 'Main');

define('SPECIAL_MIDWEEK', 'Midweek');
define('SPECIAL_REAL_MADRID', 'Real Madrid');
define('SPECIAL_CAMP', 'Camp');
define('SPECIAL_DROP_IN', 'Drop in');
define('SPECIAL_WEEKLY_DROP_IN', 'Weekly Drop in');
define('SPECIAL_OTHER', 'Other');

define('PREFIX_REGULAR_SEASON', 'JS2021-');
define('PREFIX_CAMP', 'JSC2021-');
define('PREFIX_MID_WEEK', 'JSM2021-');
define('PREFIX_OTHER', 'JSO2021-');

define('MESSAGE_TO_PLAYER', 'Player');
define('MESSAGE_TO_COACH', 'Coach');

define('SEND_BY_NOTIFICATION', 'Notification');
define('SEND_BY_EMAIL', 'Email');
define('SEND_BY_NOTIFICATION_EMAIL', 'Notification and Email');

define('INVITATION_FOR_PLAYER', 'match_players');
define('INVITATION_FOR_COACH', 'match_coaches');

define('TERM_ACCEPTED', 1);
// DEFINE ACTION IN REGISTRATIONS CONTROLLER
define('ACTION_SEND_INVITATION', 1);
define('ACTION_APPROVE_SEND_INVOICE', 2);
define('ACTION_DELETE', 3);
define('ACTION_WAITLIST', 4);
define('ACTION_WITHDRAW', 5);
define('ACTION_APPROVE_SEND_PRO_RATE_INVOICE', 6);
define('ACTION_SEND_EMAIL', 7);

// DEFINE OTHER
define('TEMPLATE_MAIL', 'Mail');

define('RECIPIENT_CC', 'cc');
define('RECIPIENT_BCC', 'bcc');

define('END_DATE_RECEIVE_REG_MAIL', "August 5");
define('PRICE_PER_CLASS', 200);

define('AQHI_IND_RSS_ENG', 'http://www.aqhi.gov.hk/epd/ddata/html/out/aqhi_ind_rss_Eng.xml');
define('WXINFO_GISLASTEST_CLOUD_TO_GROUND', 'https://www.weather.gov.hk/wxinfo/llis/gm/gislatest_c2g');

// DEFINE COORDINATES
define('LATITUDE_HKFC', 22.27557000);
define('LONGITUDE_HKFC', 114.18192300);

define('SAFE_DISTANCE', 15000.0);
define('CHECK_LINGHTNING_TIME', '+2 hours');
define('LINGHTNING_TEST', true);

define('FORMS', "forms");
define('JS_OBJECTIVES', "objectives");

define('SHOW_STATUS', 1);
define('HIDE_STATUS', 0);

//is Recurring
define('RECURRING_TYPE_DAILY', 'Daily');
define('RECURRING_TYPE_WEEKLY', 'Weekly');
define('RECURRING_TYPE_YEARLY', 'Yearly');

define('FULL_DAY_SCHEDULE_TRUE', 'Y');
define('RECURRING_TRUE', 'Y');

define('MONDAY', 'Mon');
define('TUESDAY', 'Tue');
define('WEDNESDAY', 'Wed');
define('THURSDAY', 'Thu');
define('FRIDAY', 'Fri');
define('SATURDAY', 'Sat');
define('SUNDAY', 'Sun');

// DEFINE Template Email ID
define('TEMPLATE_LAYOUT',
	[
		'DEFAULT' => 1, 
	]
);
define('TEMPLATE_INVITATION',
	[
		
		'EVENT' => 2, 
		'MATCH' => 3, 
		'TRAINING' => 4, 
		'RE_ENROLL_REGISTRATION' => 5, 
		'COACH_TO_MATCH_PLAYER' => 6,
		'COACH_TO_MATCH_COACH' => 7,
	]
);
define('TEMPLATE_REGISTRATION',
	[
		'MAIN' => 8,
		'YPL_LM' => 9,
		'SUMMER_CAMP' => 10,
		'WAITLIST_YP' => 22,
		'WAITLIST' => 11,
		'ROLLOVER' => 12,
		'APPROVE_SEASON_MAIN' => 16,
		'APPROVE_SEASON_YPL_LM' => 17,
		'APPROVE_SPECIAL' => 18,
		'CHANGE_DATES_DROP_IN' => 23,
	]
);
define('TEMPLATE_USER',
	[
		'SIGN_UP' => 13,
		'FORGOT_PASSWORD' => 14,
		'UNLOCK_ACCOUNT' => 15,
	]
);

define('BUS_BOOKING',
	[
		'REQUEST' => 19,
		'CANCEL' => 20,
		'SIZE_CHANGE' => 21,
	]
);
/******************************************************************/ 

// DEFINE SHARE PGROUPS PLAYER LISTS TYPE
define('SHARE_TYPE_PUBLIC', 1);
define('SHARE_TYPE_PRIVATE', 0);


// Polling Receiver Role
define('POLLING_RECEIVER_COACH', 1);
define('POLLING_RECEIVER_PARENT', 0);

// Type of player in match
define('TYPE_SUBSTITUTE', 'SUBSTITUTE');
define('TYPE_STARTER', 'STARTER');

define('YELLOW_CARD_EVENT', 'Yellow Card');
define('RED_CARD_EVENT', 'Red Card');
define('SCORE_EVENT', 'Score');
define('SUBSTITUTION_EVENT', 'Substitution');
define('SAVES_EVENT', 'Saves');
define('BREAK_START_EVENT', 'Break Start');
define('BREAK_END_EVENT', 'Break End');


// define Sentry config
define('SENTRY_CONFIG', [
	'dsn' => SENTRY_DSN,
	'environment' => SENTRY_ENVIRONMENT,
]);

// --- LIVE ---
// Credentials for Live
define('LIVE_ACCOUNT', '<EMAIL>');
define('LIVE_CLIENT_ID', 'ASZQ4tEs3NFeHc69qEE5j34MSz_X2CNUDKkmzetFGGcxO3ARrNawcbGRh1MYvGJ0sLQHwitfHSw28bwp');
define('LIVE_CLIENT_SECRET', 'EGFLQfEztVoatTr-y3ps4YHijdgrFwv2aY_8zHFNxbTWAlsWOwNrO2SzUthtM8DcggevDqY0flJmtGFS');

// --- SANDBOX - TEST ---
// Credentials for Sandbox for Test - App: HKFC Test
define('SANDBOX_ACCOUNT', '<EMAIL>');
define('SANDBOX_CLIENT_ID', 'Ab23gSpMuhf39cqqaT7tRdxq-2cpmoEuG7JvPuOdq-Jq7S8RrNmMwr7tYaq6ur0hT6oxVLquLf2UJw2L');
define('SANDBOX_CLIENT_SECRET', 'ENaV-ZvFa0aZHGnCd0jmo_pq27nKC3H9FhDi3QPiZIum0VuniMf227ykB3eZRdN7Llt0Tb7pSg24eooa');